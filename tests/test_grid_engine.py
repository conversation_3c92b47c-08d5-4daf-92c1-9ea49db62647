"""
网格处理引擎单元测试

为 Google Maps Grid Search 项目的网格处理引擎提供全面的单元测试。
测试覆盖分层处理、细化决策、状态更新等核心功能。

基于 TECHNICAL_DESIGN.md 中的测试策略设计。
"""

import unittest
from unittest.mock import Mock
from typing import Dict, Any

from src.grid_engine import (
    GridEngine,
    ProcessingConfig,
    ProcessingStats,
)
from src.data_models import (
    SearchOrchestration,
    GridCell,
    SearchStatus,
    create_search_orchestration,
)


class MockStateManager:
    """模拟状态管理器用于测试"""

    def __init__(self):
        self.saved_states = []
        self.save_calls = 0

    def save_state(self, orchestration):
        """模拟保存状态"""
        self.saved_states.append(orchestration)
        self.save_calls += 1

    def load_state(self):
        """模拟加载状态"""
        return None

    def load_state_with_fallback(self):
        """模拟带回退的加载状态"""
        return None


class MockAPIClient:
    """模拟API客户端用于测试"""

    def __init__(self, mock_results=None):
        self.call_count = 0
        self.mock_results = mock_results or {}

    def perform_nearby_search(
        self,
        lat: float,
        lng: float,
        radius: float,
        place_type: str,
        next_page_token: str = None,
        refine_level: int = 0,
    ) -> Dict[str, Any]:
        """模拟执行附近搜索"""
        self.call_count += 1

        # 生成模拟结果
        key = f"{lat:.6f},{lng:.6f},{radius:.1f}"
        if key in self.mock_results:
            return self.mock_results[key]

        # 模拟错误情况
        if lat == 0.0 and lng == 0.0:
            return {"status": "INVALID_REQUEST", "error_message": "Invalid coordinates"}

        # 模拟速率限制
        if self.call_count > 10:
            return {
                "status": "OVER_QUERY_LIMIT",
                "error_message": "Rate limit exceeded",
            }

        # 模拟分页数据
        if next_page_token is None:
            # 第一页，返回5个结果和下一页token
            results = []
            for i in range(5):
                results.append(
                    {
                        "place_id": f"place_{lat:.6f}_{lng:.6f}_page1_{i}",
                        "name": f"Place {i} at {lat:.6f},{lng:.6f}",
                        "geometry": {
                            "location": {
                                "lat": lat + (i * 0.0001),
                                "lng": lng + (i * 0.0001),
                            }
                        },
                    }
                )

            return {
                "results": results,
                "status": "OK",
                "next_page_token": "page2_token",
            }
        else:
            # 第二页，返回3个结果，没有更多页面
            results = []
            for i in range(3):
                results.append(
                    {
                        "place_id": f"place_{lat:.6f}_{lng:.6f}_page2_{i}",
                        "name": f"Place {i} at {lat:.6f},{lng:.6f}",
                        "geometry": {
                            "location": {
                                "lat": lat + (i * 0.0001),
                                "lng": lng + (i * 0.0001),
                            }
                        },
                    }
                )

            return {"results": results, "status": "OK"}


class TestGridEngine(unittest.TestCase):
    """测试 GridEngine 类"""

    def setUp(self):
        """测试前准备"""
        # 创建测试编排对象，使用工厂函数确保包含初始网格单元
        self.test_orchestration = create_search_orchestration(
            place_type="restaurant",
            location="Berlin, Germany",
            center_lat=52.52,
            center_lng=13.405,
            search_radius=1000.0,
        )

        # 创建模拟API客户端
        self.mock_api_client = MockAPIClient()

        # 创建模拟状态管理器
        self.mock_state_manager = MockStateManager()

        # 创建网格处理引擎
        self.config = ProcessingConfig(
            max_refinement_levels=3,
            initial_radius=1000.0,
            min_refinement_radius=100.0,
            results_threshold_for_refinement=3,  # 降低阈值便于测试
            grid_overlap_factor=0.5,
        )

        self.grid_engine = GridEngine(
            orchestration=self.test_orchestration,
            api_client=self.mock_api_client,
            state_manager=self.mock_state_manager,
            config=self.config,
        )

    def test_initialization(self):
        """测试初始化"""
        # 测试默认参数
        default_engine = GridEngine(
            self.test_orchestration, self.mock_api_client, self.mock_state_manager
        )
        self.assertEqual(default_engine.orchestration, self.test_orchestration)
        self.assertEqual(default_engine.api_client, self.mock_api_client)
        self.assertIsInstance(default_engine.config, ProcessingConfig)

        # 测试自定义参数
        self.assertEqual(self.grid_engine.orchestration, self.test_orchestration)
        self.assertEqual(self.grid_engine.api_client, self.mock_api_client)
        self.assertEqual(self.grid_engine.config, self.config)

    def test_process_next_layer_empty(self):
        """测试处理空层"""
        # 确保没有待处理单元
        for cell in self.test_orchestration.cells.values():
            cell.update_status(SearchStatus.SEARCH_COMPLETE)

        has_more, stats = self.grid_engine.process_next_layer()

        # 验证结果
        self.assertFalse(has_more)
        self.assertIsInstance(stats, ProcessingStats)
        self.assertEqual(stats.total_cells, 0)
        self.assertEqual(stats.processed_cells, 0)

        # 验证层状态
        self.assertIn(0, self.test_orchestration.completed_layers)
        self.assertEqual(self.test_orchestration.current_layer, 1)

    def test_process_single_cell_success(self):
        """测试成功处理单个单元"""
        # 获取根单元
        root_cell_id = self.test_orchestration.layers[0][0]
        root_cell = self.test_orchestration.cells[root_cell_id]

        # 配置模拟API返回少量结果以避免细化
        lat = root_cell.center_lat
        lng = root_cell.center_lng
        mock_results = {
            f"{lat:.6f},{lng:.6f},1000.0": {
                "results": [
                    {"place_id": f"place_{i}"} for i in range(2)
                ],  # 2个地点，小于阈值3
                "status": "OK",
            }
        }
        self.mock_api_client.mock_results = mock_results

        # 处理单个单元
        stats = ProcessingStats()
        self.grid_engine._process_single_cell(root_cell, stats)

        # 验证结果
        self.assertEqual(root_cell.status, SearchStatus.SEARCH_COMPLETE)
        self.assertEqual(root_cell.results_count, 2)
        self.assertEqual(len(root_cell.place_ids), 2)
        self.assertEqual(stats.processed_cells, 1)
        self.assertEqual(stats.api_calls, 1)
        self.assertEqual(self.mock_api_client.call_count, 1)

    def test_process_single_cell_refinement_needed(self):
        """测试需要细化的单元处理"""
        # 创建一个需要细化的单元
        cell = GridCell(
            cell_id="L0-refine",
            status=SearchStatus.PENDING,
            layer_id=0,
            center_lat=52.52,
            center_lng=13.405,
            search_radius=1000.0,
        )
        self.test_orchestration.add_cell(cell)

        # 配置模拟API返回大量结果
        mock_results = {
            "52.520000,13.405000,1000.0": {
                "results": [
                    {"place_id": f"place_{i}"} for i in range(10)
                ],  # 10个地点，超过阈值
                "status": "OK",
            }
        }
        self.mock_api_client.mock_results = mock_results

        # 处理单元
        stats = ProcessingStats()
        self.grid_engine._process_single_cell(cell, stats)

        # 验证结果
        self.assertEqual(cell.status, SearchStatus.REFINEMENT_NEEDED)
        self.assertEqual(cell.results_count, 10)
        self.assertEqual(stats.refined_cells, 1)

    def test_process_single_cell_failure(self):
        """测试处理单元失败 - 异常应该向上传播"""
        # 创建模拟API客户端，使其抛出异常
        failing_api_client = Mock()
        failing_api_client.perform_nearby_search.side_effect = Exception("API Error")

        # 创建新的引擎使用失败的API客户端
        failing_engine = GridEngine(
            orchestration=self.test_orchestration,
            api_client=failing_api_client,
            state_manager=self.mock_state_manager,
            config=self.config,
        )

        # 获取根单元
        root_cell_id = self.test_orchestration.layers[0][0]
        root_cell = self.test_orchestration.cells[root_cell_id]

        # 处理单元 - 现在应该抛出异常
        stats = ProcessingStats()
        with self.assertRaises(Exception) as context:
            failing_engine._process_single_cell(root_cell, stats)

        # 验证异常消息
        self.assertEqual(str(context.exception), "API Error")

        # 验证状态没有被错误地标记为失败（因为异常被抛出了）
        # 单元应该保持 PROCESSING 状态，因为异常在中途被抛出
        self.assertEqual(root_cell.status, SearchStatus.PROCESSING)
        self.assertEqual(stats.failed_cells, 0)

    def test_perform_search(self):
        """测试执行搜索"""
        # 获取根单元
        root_cell_id = self.test_orchestration.layers[0][0]
        root_cell = self.test_orchestration.cells[root_cell_id]

        # 创建一个单页结果的模拟客户端
        single_page_client = MockAPIClient()
        single_page_client.mock_results = {
            f"{root_cell.center_lat:.6f},{root_cell.center_lng:.6f},{root_cell.search_radius:.1f}": {
                "results": [
                    {"place_id": "place1"},
                    {"place_id": "place2"},
                    {"place_id": "place3"},
                ],
                "status": "OK",
            }
        }

        # 临时替换API客户端
        original_client = self.grid_engine.api_client
        self.grid_engine.api_client = single_page_client

        try:
            # 执行搜索
            results = self.grid_engine._perform_search(root_cell)

            # 验证结果
            self.assertIn("results", results)
            self.assertEqual(results["status"], "OK")
            self.assertEqual(single_page_client.call_count, 1)
            self.assertEqual(results["api_calls_made"], 1)
        finally:
            # 恢复原始客户端
            self.grid_engine.api_client = original_client

    def test_perform_search_multipage(self):
        """测试执行多页搜索"""
        # 获取根单元
        root_cell_id = self.test_orchestration.layers[0][0]
        root_cell = self.test_orchestration.cells[root_cell_id]

        # 设置最大API调用次数为3（允许获取多页）
        self.grid_engine.config.max_api_calls_per_cell = 3

        # 重置API调用计数
        self.mock_api_client.call_count = 0

        # 执行搜索
        results = self.grid_engine._perform_search(root_cell)

        # 验证结果
        self.assertIn("results", results)
        self.assertEqual(results["status"], "OK")
        self.assertEqual(
            self.mock_api_client.call_count, 2
        )  # 应该调用了2次（第一页和第二页）
        self.assertEqual(results["api_calls_made"], 2)
        self.assertEqual(len(results["results"]), 8)  # 第一页5个 + 第二页3个 = 8个结果

        # 验证结果包含来自两页的数据
        place_ids = [result["place_id"] for result in results["results"]]
        page1_ids = [pid for pid in place_ids if "page1" in pid]
        page2_ids = [pid for pid in place_ids if "page2" in pid]
        self.assertEqual(len(page1_ids), 5)
        self.assertEqual(len(page2_ids), 3)

    def test_handle_search_results_no_refinement(self):
        """测试处理搜索结果（不需要细化）"""
        # 创建单元
        cell = GridCell(
            cell_id="L0-test",
            status=SearchStatus.PROCESSING,
            layer_id=0,
            center_lat=52.52,
            center_lng=13.405,
            search_radius=1000.0,
        )

        # 模拟搜索结果（少于阈值）
        results = {
            "results": [
                {"place_id": "place1"},
                {"place_id": "place2"},  # 只有2个地点，小于阈值3
            ],
            "status": "OK",
        }

        # 处理结果
        stats = ProcessingStats()
        self.grid_engine._handle_search_results(cell, results, stats)

        # 验证结果
        self.assertEqual(cell.status, SearchStatus.SEARCH_COMPLETE)
        self.assertEqual(cell.results_count, 2)
        self.assertEqual(len(cell.place_ids), 2)
        self.assertEqual(stats.places_found, 2)
        self.assertEqual(stats.refined_cells, 0)

    def test_handle_search_results_with_refinement(self):
        """测试处理搜索结果（需要细化）"""
        # 创建单元
        cell = GridCell(
            cell_id="L0-test",
            status=SearchStatus.PROCESSING,
            layer_id=0,
            center_lat=52.52,
            center_lng=13.405,
            search_radius=1000.0,
        )

        # 模拟搜索结果（超过阈值）
        results = {
            "results": [
                {"place_id": "place1"},
                {"place_id": "place2"},
                {"place_id": "place3"},
                {"place_id": "place4"},  # 4个地点，超过阈值3
            ],
            "status": "OK",
        }

        # 处理结果
        stats = ProcessingStats()
        self.grid_engine._handle_search_results(cell, results, stats)

        # 验证结果
        self.assertEqual(cell.status, SearchStatus.REFINEMENT_NEEDED)
        self.assertEqual(cell.results_count, 4)
        self.assertEqual(len(cell.place_ids), 4)
        self.assertEqual(stats.places_found, 4)
        self.assertEqual(stats.refined_cells, 1)

    def test_calculate_refined_radius(self):
        """测试计算细化半径"""
        # 创建父单元
        parent_cell = GridCell(
            cell_id="L0-parent",
            status=SearchStatus.REFINEMENT_NEEDED,
            layer_id=0,
            center_lat=52.52,
            center_lng=13.405,
            search_radius=1000.0,
        )

        # 计算细化半径
        refined_radius = self.grid_engine._calculate_refined_radius(parent_cell)

        # 验证结果（应该是父半径的一半）
        self.assertEqual(refined_radius, 500.0)

    def test_generate_child_points_root_layer(self):
        """测试生成子网格点（根层）"""
        # 创建父单元
        parent_cell = GridCell(
            cell_id="L0-parent",
            status=SearchStatus.REFINEMENT_NEEDED,
            layer_id=0,
            center_lat=52.52,
            center_lng=13.405,
            search_radius=500.0,
        )

        # 生成子点
        child_points = self.grid_engine._generate_child_points(parent_cell, 250.0)

        # 验证结果
        self.assertIsInstance(child_points, list)
        self.assertGreater(len(child_points), 0)
        # 验证点不包含父单元中心
        for lat, lng in child_points:
            self.assertFalse(abs(lat - 52.52) < 1e-6 and abs(lng - 13.405) < 1e-6)

    def test_plan_next_layer(self):
        """测试规划下一层"""
        # 创建需要细化的父单元
        parent_cell = GridCell(
            cell_id="L0-parent",
            status=SearchStatus.REFINEMENT_NEEDED,
            layer_id=0,
            center_lat=52.52,
            center_lng=13.405,
            search_radius=1000.0,
        )
        self.test_orchestration.add_cell(parent_cell)

        # 规划下一层
        self.grid_engine._plan_next_layer([parent_cell])

        # 验证结果
        self.assertEqual(parent_cell.status, SearchStatus.REFINEMENT_COMPLETE)
        self.assertGreater(len(parent_cell.children_ids), 0)

        # 验证子单元已创建
        next_layer_cells = self.test_orchestration.get_layer_cells(1)
        self.assertGreater(len(next_layer_cells), 0)

        # 验证子单元属性
        for child_cell in next_layer_cells:
            self.assertEqual(child_cell.layer_id, 1)
            self.assertEqual(child_cell.parent_id, "L0-parent")
            self.assertEqual(child_cell.status, SearchStatus.PENDING)

    def test_has_pending_work(self):
        """测试检查是否有待处理工作"""
        # 初始状态下应该有待处理工作（根单元）
        self.assertTrue(self.grid_engine.has_pending_work())

        # 将所有单元标记为完成
        for cell in self.test_orchestration.cells.values():
            cell.update_status(SearchStatus.SEARCH_COMPLETE)

        # 现在应该没有待处理工作
        self.assertFalse(self.grid_engine.has_pending_work())

    def test_get_current_layer(self):
        """测试获取当前层"""
        self.assertEqual(self.grid_engine.get_current_layer(), 0)

    def test_get_processing_stats(self):
        """测试获取处理统计信息"""
        stats = self.grid_engine.get_processing_stats()
        self.assertIsInstance(stats, dict)
        self.assertIn("current_layer", stats)
        self.assertIn("completed_layers", stats)
        self.assertIn("total_cells", stats)
        self.assertIn("metrics", stats)

    def test_process_next_layer_integration(self):
        """测试处理下一层（集成测试）"""
        # 配置模拟API返回少量结果以避免细化
        root_cell_id = self.test_orchestration.layers[0][0]
        lat = self.test_orchestration.cells[root_cell_id].center_lat
        lng = self.test_orchestration.cells[root_cell_id].center_lng
        mock_results = {
            f"{lat:.6f},{lng:.6f},1000.0": {
                "results": [
                    {"place_id": f"place_{i}"} for i in range(2)
                ],  # 2个地点，小于阈值3
                "status": "OK",
            }
        }
        self.mock_api_client.mock_results = mock_results

        # 处理第0层
        has_more, stats = self.grid_engine.process_next_layer()

        # 验证结果
        self.assertIsInstance(has_more, bool)
        self.assertIsInstance(stats, ProcessingStats)
        self.assertEqual(stats.total_cells, 1)  # 只有根单元
        self.assertEqual(stats.processed_cells, 1)
        self.assertEqual(stats.api_calls, 1)

        # 验证根单元状态
        root_cell = self.test_orchestration.cells[root_cell_id]
        self.assertIn(
            root_cell.status,
            [
                SearchStatus.SEARCH_COMPLETE,
                SearchStatus.REFINEMENT_NEEDED,
                SearchStatus.REFINEMENT_COMPLETE,
            ],
        )

        # 验证层状态
        self.assertIn(0, self.test_orchestration.completed_layers)
        self.assertEqual(self.test_orchestration.current_layer, 1)

    def test_layer_processing_with_refinement(self):
        """测试带细化的层处理"""
        # 配置引擎以确保细化发生
        self.config.results_threshold_for_refinement = 2
        self.grid_engine.config = self.config

        # 配置模拟API返回大量结果以触发细化
        root_cell_id = self.test_orchestration.layers[0][0]
        lat = self.test_orchestration.cells[root_cell_id].center_lat
        lng = self.test_orchestration.cells[root_cell_id].center_lng

        mock_results = {
            f"{lat:.6f},{lng:.6f},1000.0": {
                "results": [
                    {"place_id": f"place_{i}"} for i in range(5)
                ],  # 5个地点，超过阈值2
                "status": "OK",
            }
        }
        self.mock_api_client.mock_results = mock_results

        # 处理第0层
        has_more, stats = self.grid_engine.process_next_layer()

        # 验证细化已触发
        self.assertTrue(has_more)  # 应该有更多层需要处理
        self.assertEqual(stats.refined_cells, 1)

        # 验证根单元状态（在规划下一层后应该是REFINEMENT_COMPLETE）
        root_cell = self.test_orchestration.cells[root_cell_id]
        self.assertEqual(root_cell.status, SearchStatus.REFINEMENT_COMPLETE)

        # 验证下一层已规划
        next_layer_cells = self.test_orchestration.get_layer_cells(1)
        self.assertGreater(len(next_layer_cells), 0)

    def test_perform_search_api_error(self):
        """测试API错误处理"""
        # 获取根单元
        root_cell_id = self.test_orchestration.layers[0][0]
        root_cell = self.test_orchestration.cells[root_cell_id]

        # 创建模拟API客户端，返回错误状态
        error_client = MockAPIClient()
        error_client.mock_results = {
            f"{root_cell.center_lat:.6f},{root_cell.center_lng:.6f},{root_cell.search_radius:.1f}": {
                "status": "INVALID_REQUEST",
                "error_message": "Invalid request parameters",
            }
        }

        # 临时替换API客户端
        original_client = self.grid_engine.api_client
        self.grid_engine.api_client = error_client

        try:
            # 执行搜索
            results = self.grid_engine._perform_search(root_cell)

            # 验证结果
            self.assertEqual(results["status"], "INVALID_REQUEST")
            self.assertEqual(len(results["results"]), 0)
            self.assertEqual(results["api_calls_made"], 1)
        finally:
            # 恢复原始客户端
            self.grid_engine.api_client = original_client

    def test_perform_search_empty_response(self):
        """测试空响应处理"""
        # 获取根单元
        root_cell_id = self.test_orchestration.layers[0][0]
        root_cell = self.test_orchestration.cells[root_cell_id]

        # 直接创建一个返回ZERO结果的模拟客户端
        class EmptyMockClient:
            def __init__(self):
                self.call_count = 0

            def perform_nearby_search(
                self,
                lat: float,
                lng: float,
                radius: float,
                place_type: str,
                next_page_token: str = None,
                refine_level: int = 0,
            ) -> Dict[str, Any]:
                self.call_count += 1
                return {"status": "ZERO_RESULTS", "results": []}

        empty_client = EmptyMockClient()

        # 临时替换API客户端
        original_client = self.grid_engine.api_client
        self.grid_engine.api_client = empty_client

        try:
            # 执行搜索
            results = self.grid_engine._perform_search(root_cell)

            # 验证结果
            self.assertEqual(results["status"], "ZERO_RESULTS")
            self.assertEqual(len(results["results"]), 0)
            self.assertEqual(results["api_calls_made"], 1)
        finally:
            # 恢复原始客户端
            self.grid_engine.api_client = original_client

    def test_perform_search_rate_limit(self):
        """测试速率限制处理"""
        # 获取根单元
        root_cell_id = self.test_orchestration.layers[0][0]
        root_cell = self.test_orchestration.cells[root_cell_id]

        # 创建模拟API客户端，设置调用计数来触发速率限制
        rate_limit_client = MockAPIClient()
        rate_limit_client.call_count = 11  # 超过限制阈值

        # 临时替换API客户端
        original_client = self.grid_engine.api_client
        self.grid_engine.api_client = rate_limit_client

        try:
            # 执行搜索
            results = self.grid_engine._perform_search(root_cell)

            # 验证结果
            self.assertEqual(results["status"], "OVER_QUERY_LIMIT")
            self.assertEqual(len(results["results"]), 0)
            self.assertEqual(results["api_calls_made"], 1)
        finally:
            # 恢复原始客户端
            self.grid_engine.api_client = original_client


class TestFactoryFunctions(unittest.TestCase):
    """测试工厂函数"""

    def test_create_grid_engine(self):
        """测试创建网格处理引擎"""
        # 创建测试对象
        orchestration = SearchOrchestration(
            task_id="test-task-2", place_type="cafe", location="Hamburg, Germany"
        )

        mock_api_client = MockAPIClient()
        mock_state_manager = MockStateManager()
        config = ProcessingConfig()

        # 创建引擎
        engine = GridEngine(
            orchestration=orchestration,
            api_client=mock_api_client,
            state_manager=mock_state_manager,
            config=config,
            visualization_callback=None
        )

        # 验证结果
        self.assertIsInstance(engine, GridEngine)
        self.assertEqual(engine.orchestration, orchestration)
        self.assertEqual(engine.api_client, mock_api_client)
        self.assertEqual(engine.config, config)


if __name__ == "__main__":
    unittest.main()
