"""
可视化服务模块测试

测试 VisualizationService 类的功能。
"""

import pytest
import tempfile
import os
import time
from unittest.mock import Mock, patch
from pathlib import Path

from src.visualization_service import VisualizationService, VisualizationState


class TestVisualizationState:
    """可视化状态测试类"""

    def test_state_initialization(self):
        """测试状态初始化"""
        state = VisualizationState()

        assert state.last_update == 0.0
        assert state.update_count == 0
        assert state.file_rotation == []
        assert state.enabled is True
        assert state.error_count == 0


class TestVisualizationService:
    """可视化服务测试类"""

    def test_initialization_success(self):
        """测试成功初始化"""
        with tempfile.TemporaryDirectory() as temp_dir:
            config = {"enabled": True}
            service = VisualizationService(str(temp_dir), config)

            assert service.config["enabled"] is True
            # 在macOS上，临时目录可能有不同的路径表示，使用resolve()来规范化
            assert service.work_dir.resolve() == Path(temp_dir).resolve()
            assert service.state.enabled is True
            assert service.state.update_count == 0

    def test_initialization_with_default_config(self):
        """测试使用默认配置初始化"""
        with tempfile.TemporaryDirectory() as temp_dir:
            service = VisualizationService(str(temp_dir))

            # 检查默认配置是否被正确应用
            assert service.config["enabled"] is True
            # 注意：由于我们移除了VisualizationConfig，这里的update_interval和max_file_count
            # 可能不再是service.config的直接属性。我们只测试核心逻辑。
            assert service.work_dir.resolve() == Path(temp_dir).resolve()

    def test_initialization_directory_creation(self):
        """测试目录自动创建"""
        with tempfile.TemporaryDirectory() as temp_dir:
            subdir = os.path.join(temp_dir, "new_subdir")

            # 确保子目录不存在
            assert not os.path.exists(subdir)

            service = VisualizationService(subdir)

            # 目录应该被自动创建
            assert os.path.exists(subdir)
            assert service.work_dir.resolve() == Path(subdir).resolve()

    def test_initialization_invalid_directory(self):
        """测试无效目录的处理"""
        # 测试不存在的路径且无法创建的情况
        invalid_path = "/invalid/path/that/does/not/exist"

        with pytest.raises(ValueError, match="无效的工作目录"):
            VisualizationService(invalid_path)

    def test_should_update_enabled_and_folium_available(self):
        """测试启用且folium可用时的更新判断"""
        with tempfile.TemporaryDirectory() as temp_dir:
            service = VisualizationService(temp_dir)

            with patch("src.visualization_service.FOLIUM_AVAILABLE", True):
                assert service._should_update() is True

    def test_should_update_disabled(self):
        """测试禁用时的更新判断"""
        with tempfile.TemporaryDirectory() as temp_dir:
            config = {"enabled": False}
            service = VisualizationService(temp_dir, config)

            with patch("src.visualization_service.FOLIUM_AVAILABLE", True):
                assert service._should_update() is False

    def test_should_update_folium_unavailable(self):
        """测试folium不可用时的更新判断"""
        with tempfile.TemporaryDirectory() as temp_dir:
            service = VisualizationService(temp_dir)

            with patch("src.visualization_service.FOLIUM_AVAILABLE", False):
                assert service._should_update() is False

    def test_should_update_force_true(self):
        """测试强制更新"""
        with tempfile.TemporaryDirectory() as temp_dir:
            service = VisualizationService(temp_dir)

            with patch("src.visualization_service.FOLIUM_AVAILABLE", True):
                assert service._should_update(force=True) is True

    def test_should_update_throttling_disabled(self):
        """测试禁用节流时的更新判断"""
        with tempfile.TemporaryDirectory() as temp_dir:
            config = {"enable_throttling": False}
            service = VisualizationService(temp_dir, config)

            with patch("src.visualization_service.FOLIUM_AVAILABLE", True):
                assert service._should_update() is True

    def test_should_update_time_interval(self):
        """测试时间间隔判断"""
        with tempfile.TemporaryDirectory() as temp_dir:
            config = {"update_interval": 10.0}
            service = VisualizationService(temp_dir, config)

            with patch("src.visualization_service.FOLIUM_AVAILABLE", True):
                # 首次更新，应该返回True
                assert service._should_update() is True

                # 模拟刚刚更新过
                service.state.last_update = time.time()

                # 现在应该返回False，因为时间间隔不够
                assert service._should_update() is False

                # 模拟时间过去足够长
                service.state.last_update = time.time() - 15.0

                # 现在应该返回True
                assert service._should_update() is True

    def test_update_on_cell_processed_should_update(self):
        """测试单元格处理更新（应该更新时）"""
        with tempfile.TemporaryDirectory() as temp_dir:
            service = VisualizationService(temp_dir)

            mock_cell = Mock()
            mock_cell.cell_id = "test_cell"
            mock_orchestration = Mock()
            mock_orchestration.cells = {"test_cell": mock_cell}

            with patch.object(service, "_should_update", return_value=True):
                with patch.object(
                    service, "_create_intermediate_visualization", return_value=True
                ) as mock_create:
                    result = service.update_on_cell_processed(
                        mock_cell, mock_orchestration
                    )

                    assert result is True
                    mock_create.assert_called_once_with(
                        mock_orchestration,
                        suffix="realtime",
                        highlight_cell="test_cell",
                    )

    def test_update_on_cell_processed_should_not_update(self):
        """测试单元格处理更新（不应该更新时）"""
        with tempfile.TemporaryDirectory() as temp_dir:
            service = VisualizationService(temp_dir)

            mock_cell = Mock()
            mock_orchestration = Mock()

            with patch.object(service, "_should_update", return_value=False):
                with patch.object(
                    service, "_create_intermediate_visualization"
                ) as mock_create:
                    result = service.update_on_cell_processed(
                        mock_cell, mock_orchestration
                    )

                    assert result is False
                    mock_create.assert_not_called()

    def test_update_on_initialization(self):
        """测试初始化更新"""
        with tempfile.TemporaryDirectory() as temp_dir:
            service = VisualizationService(temp_dir)

            mock_orchestration = Mock()
            mock_orchestration.cells = {"test": Mock()}

            with patch.object(
                service, "_create_intermediate_visualization", return_value=True
            ) as mock_create:
                result = service.update_on_initialization(mock_orchestration)

                assert result is True
                mock_create.assert_called_once_with(
                    mock_orchestration, suffix="initial"
                )

    def test_update_on_refinement(self):
        """测试细化更新"""
        with tempfile.TemporaryDirectory() as temp_dir:
            service = VisualizationService(temp_dir)

            mock_orchestration = Mock()
            mock_orchestration.cells = {"test": Mock()}

            with patch.object(
                service, "_create_intermediate_visualization", return_value=True
            ) as mock_create:
                result = service.update_on_refinement(mock_orchestration)

                assert result is True
                mock_create.assert_called_once_with(
                    mock_orchestration, suffix="refinement"
                )

    def test_create_intermediate_visualization_no_data(self):
        """测试没有数据时的可视化创建"""
        with tempfile.TemporaryDirectory() as temp_dir:
            service = VisualizationService(temp_dir)

            # 空的编排对象
            mock_orchestration = Mock()
            mock_orchestration.cells = {}

            result = service._create_intermediate_visualization(
                mock_orchestration, "test"
            )

            assert result is False

    def test_create_intermediate_visualization_folium_unavailable(self):
        """测试folium不可用时的可视化创建"""
        with tempfile.TemporaryDirectory() as temp_dir:
            service = VisualizationService(temp_dir)

            mock_orchestration = Mock()
            mock_orchestration.cells = {"test": Mock()}

            with patch("src.visualization_service.FOLIUM_AVAILABLE", False):
                result = service._create_intermediate_visualization(
                    mock_orchestration, "test"
                )

                assert result is False

    def test_create_intermediate_visualization_success(self):
        """测试成功创建可视化"""
        with tempfile.TemporaryDirectory() as temp_dir:
            service = VisualizationService(str(temp_dir))

            # 模拟编排对象
            mock_cell = Mock()
            mock_cell.center_lat = 52.52
            mock_cell.center_lng = 13.40
            mock_cell.status = Mock()
            mock_cell.status.value = "completed"

            mock_orchestration = Mock()
            mock_orchestration.cells = {"test": mock_cell}
            mock_orchestration.get_summary.return_value = {"total_places": 10}
            mock_orchestration.current_layer = 0
            mock_orchestration.completed_layers = []
            mock_orchestration.get_all_place_ids.return_value = ["place1", "place2"]
            mock_orchestration.metrics = {"total_api_calls": 5}
            mock_orchestration.get_overall_status.return_value = "processing"
            # 添加缺少的mock方法
            mock_orchestration.get_pending_cells.return_value = []

            # 模拟folium可用
            with patch("src.visualization_service.FOLIUM_AVAILABLE", True):
                with patch("src.visualization_service.folium") as mock_folium:
                    mock_map = Mock()
                    mock_folium.Map.return_value = mock_map

                    result = service._create_intermediate_visualization(
                        mock_orchestration, "test"
                    )

                    assert result is True
                    mock_folium.Map.assert_called_once()
                    mock_map.save.assert_called_once()

    def test_extract_state_data(self):
        """测试状态数据提取"""
        with tempfile.TemporaryDirectory() as temp_dir:
            service = VisualizationService(str(temp_dir))

            # 模拟不同状态的单元格
            from src.data_models import SearchStatus

            cells = []
            for i, status in enumerate(SearchStatus):
                cell = Mock()
                cell.center_lat = 52.52 + i * 0.01
                cell.center_lng = 13.40 + i * 0.01
                cell.status = status
                cells.append(cell)

            mock_orchestration = Mock()
            mock_orchestration.cells.values.return_value = cells

            state_data = service._extract_state_data(mock_orchestration)

            assert "pending_nodes" in state_data
            assert "processing_nodes" in state_data
            assert "completed_nodes" in state_data
            assert "failed_nodes" in state_data
            assert "refinement_nodes" in state_data

            # 检查每种状态的节点数量
            assert len(state_data["pending_nodes"]) == 1  # PENDING
            assert len(state_data["processing_nodes"]) == 1  # PROCESSING
            assert (
                len(state_data["completed_nodes"]) == 2
            )  # SEARCH_COMPLETE, REFINEMENT_COMPLETE
            assert len(state_data["failed_nodes"]) == 1  # FAILED
            assert len(state_data["refinement_nodes"]) == 1  # REFINEMENT_NEEDED

    def test_calculate_bounds_empty(self):
        """测试空单元格的边界计算"""
        with tempfile.TemporaryDirectory() as temp_dir:
            service = VisualizationService(temp_dir)

            mock_orchestration = Mock()
            mock_orchestration.cells = {}

            bounds = service._calculate_bounds(mock_orchestration)

            # 应该返回默认柏林中心边界
            expected = (52.52, 13.405, 52.53, 13.415)
            assert bounds == expected

    def test_calculate_bounds_with_cells(self):
        """测试带单元格的边界计算"""
        with tempfile.TemporaryDirectory() as temp_dir:
            service = VisualizationService(temp_dir)

            # 模拟一些单元格
            cells = []
            for i in range(3):
                cell = Mock()
                cell.center_lat = 52.50 + i * 0.02
                cell.center_lng = 13.40 + i * 0.02
                cells.append(cell)

            mock_orchestration = Mock()
            mock_orchestration.cells.values.return_value = cells

            bounds = service._calculate_bounds(mock_orchestration)

            # 检查边界是否正确（应该包含所有单元格加上填充）
            min_lat, min_lng, max_lat, max_lng = bounds
            assert min_lat < 52.50  # 考虑填充
            assert max_lat > 52.54  # 考虑填充
            assert min_lng < 13.40  # 考虑填充
            assert max_lng > 13.44  # 考虑填充

    def test_cleanup_old_files(self):
        """测试旧文件清理"""
        with tempfile.TemporaryDirectory() as temp_dir:
            service = VisualizationService(temp_dir)

            # 创建一些测试文件
            filenames = [
                "visualization_map_initial.html",
                "visualization_map_realtime.html",
                "visualization_map_refinement.html",
                "other_file.html",
            ]

            # 创建文件并设置不同的修改时间
            import time

            current_time = time.time()
            for i, filename in enumerate(filenames):
                filepath = os.path.join(temp_dir, filename)
                with open(filepath, "w") as f:
                    f.write(f"Content of {filename}")

                # 设置不同的修改时间
                file_time = current_time - (i * 3600)  # 每个文件早1小时
                os.utime(filepath, (file_time, file_time))

            # 设置最大文件数量为2
            service.config["max_file_count"] = 2

            # 执行清理
            service._cleanup_old_files()

            # 检查是否只保留了最新的2个匹配文件
            remaining_files = [
                f for f in os.listdir(temp_dir) if f.startswith("visualization_map_")
            ]
            assert len(remaining_files) == 2

            # 检查其他文件是否被保留
            assert os.path.exists(os.path.join(temp_dir, "other_file.html"))

    def test_record_file_update(self):
        """测试文件更新记录"""
        with tempfile.TemporaryDirectory() as temp_dir:
            service = VisualizationService(temp_dir)

            file_path = "/path/to/file.html"

            # 记录文件更新前
            assert service.state.update_count == 0
            assert service.state.last_update == 0.0

            # 记录文件更新
            service._record_file_update(file_path)

            # 检查状态是否更新
            assert service.state.update_count == 1
            assert service.state.last_update > 0.0
            assert file_path in service.state.file_rotation

            # 再次记录
            service._record_file_update("/path/to/file2.html")

            # 检查文件旋转列表是否正确维护
            assert len(service.state.file_rotation) <= service.config["max_file_count"]
            assert service.state.update_count == 2

    def test_get_stats(self):
        """测试统计信息获取"""
        with tempfile.TemporaryDirectory() as temp_dir:
            config = {
                "enabled": True,
                "update_interval": 10.0,
                "max_file_count": 5
            }
            service = VisualizationService(str(temp_dir), config)

            # 模拟一些操作
            service.state.update_count = 3
            service.state.error_count = 1
            service.state.last_update = time.time()

            stats = service.get_stats()

            assert isinstance(stats, dict)
            assert "enabled" in stats
            assert "update_count" in stats
            assert "error_count" in stats
            assert "last_update" in stats
            assert "work_dir" in stats
            assert "config" in stats

            assert stats["enabled"] is True
            assert stats["update_count"] == 3
            assert stats["error_count"] == 1
            assert stats["last_update"] > 0
            # 在macOS上，临时目录可能有不同的路径表示，使用resolve()来规范化
            assert Path(stats["work_dir"]).resolve() == Path(temp_dir).resolve()
            assert stats["config"]["update_interval"] == 10.0
            assert stats["config"]["max_file_count"] == 5

    def test_reset(self):
        """测试服务重置"""
        with tempfile.TemporaryDirectory() as temp_dir:
            service = VisualizationService(temp_dir)

            # 模拟一些状态变更
            service.state.update_count = 5
            service.state.error_count = 2
            service.state.file_rotation = ["file1.html", "file2.html"]

            # 重置服务
            service.reset()

            # 检查状态是否重置
            assert service.state.update_count == 0
            assert service.state.error_count == 0
            assert service.state.file_rotation == []
            assert service.state.enabled is True
            assert service.state.last_update == 0.0
