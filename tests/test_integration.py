"""
简化的集成测试
"""

import unittest
import tempfile
import os
from unittest.mock import Mock, patch

from src.config import Config
from src.api_client import APIClient
from src.data_models import create_search_orchestration
from src.state_manager import StateManager
from src.grid_engine import GridEngine, ProcessingConfig


class TestSimpleIntegration(unittest.TestCase):
    """简化的集成测试类"""

    def setUp(self):
        """设置测试环境"""
        self.temp_dir = tempfile.mkdtemp()
        
        # 创建简化的配置
        self.config = Config(
            api_key="test_key",
            dry_run=True,
            work_dir=self.temp_dir,
            max_calls=10
        )

    def tearDown(self):
        """清理测试环境"""
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)

    def test_api_client_creation(self):
        """测试API客户端创建"""
        client = APIClient(self.config)
        
        self.assertIsNotNone(client)
        self.assertEqual(client.config, self.config)

    def test_state_manager_creation(self):
        """测试状态管理器创建"""
        state_file = os.path.join(self.temp_dir, "test_state.json")
        state_manager = StateManager(state_file)
        
        self.assertIsNotNone(state_manager)

    def test_orchestration_creation(self):
        """测试搜索编排对象创建"""
        bounds = (52.5, 13.4, 52.6, 13.5)
        orchestration = create_search_orchestration(
            bounds=bounds,
            initial_radius=1000,
            initial_grid_step=800
        )
        
        self.assertIsNotNone(orchestration)
        self.assertEqual(len(orchestration.cells), 1)

    def test_basic_workflow(self):
        """测试基本工作流程"""
        # 创建组件
        api_client = APIClient(self.config)
        state_file = os.path.join(self.temp_dir, "workflow_state.json")
        state_manager = StateManager(state_file)
        
        # 创建搜索编排
        bounds = (52.5, 13.4, 52.6, 13.5)
        orchestration = create_search_orchestration(
            bounds=bounds,
            initial_radius=1000,
            initial_grid_step=800
        )
        
        # 创建网格引擎
        processing_config = ProcessingConfig()
        engine = GridEngine(
            orchestration=orchestration,
            api_client=api_client,
            state_manager=state_manager,
            config=processing_config,
            visualization_callback=None
        )
        
        # 验证引擎创建成功
        self.assertIsNotNone(engine)
        
        # 执行一次处理
        has_more, stats = engine.process_next_layer()
        
        # 验证处理结果
        self.assertIsNotNone(stats)
        self.assertGreaterEqual(stats.processed_cells, 0)


if __name__ == '__main__':
    unittest.main()
