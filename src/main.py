#!/usr/bin/env python3
"""
重构后的主程序

大幅简化的主程序流程，保留所有高级功能但减少复杂性。
"""

import time
import os

from src.config import create_config, create_parser
from src.api_client import create_api_client
from src.grid_algorithms import generate_grid_points, calculate_search_parameters
from src.data_models import create_search_orchestration_with_grid, SearchStatus
from src.state_manager import StateManager
from src.grid_engine import GridEngine

from src.visualization import generate_visualization_from_orchestration, save_map_data
from src.logging_config import configure_logging, get_logger
from src.exceptions import SearchError, ConfigError

logger = get_logger(__name__)


def setup_search_area(config, api_client) -> tuple:
    """设置搜索区域并返回边界

    Args:
        config: 统一配置对象
        api_client: API客户端

    Returns:
        tuple: 搜索边界 (min_lat, min_lng, max_lat, max_lng)
    """
    # 根据配置获取搜索边界
    bounds = config.get_search_bounds()
    
    if bounds:
        area_name = config.get_location_name()
        logger.info(f"使用配置的搜索区域: {area_name}")
        logger.info(f"搜索区域边界: {bounds}")
    else:
        # 使用API获取边界框
        bounds = api_client.get_bounding_box(config.location)
        if bounds is None:
            raise SearchError(f"无法获取位置 '{config.location}' 的边界框")
        logger.info(f"使用API获取的边界框: {bounds}")
    
    # 保存边界到配置
    config.search_bounds = bounds
    return bounds


def create_or_load_orchestration(state_manager, config, bounds):
    """创建或加载搜索编排对象

    Args:
        state_manager: 状态管理器
        config: 统一配置对象
        bounds: 搜索边界

    Returns:
        SearchOrchestration: 搜索编排对象
    """
    try:
        # 尝试加载现有状态
        orchestration = state_manager.load_state_with_fallback()
        if orchestration is not None:
            logger.info("从现有状态加载搜索编排对象")
            return orchestration
    except Exception as e:
        logger.warning(f"加载状态失败: {e}，将创建新的编排对象")
    
    # 创建新的编排对象
    logger.info("创建新的搜索编排对象")
    
    # 确定初始参数
    if config.initial_radius is not None and config.initial_grid_step is not None:
        initial_radius = config.initial_radius
        initial_grid_step = config.initial_grid_step
        logger.info(f"使用用户指定参数: 半径={initial_radius}m, 步长={initial_grid_step}m")
    else:
        # 自动计算参数
        initial_radius, initial_grid_step, _ = calculate_search_parameters(
            bounds,
            config.max_radius,
            config.min_refinement_radius,
            config.max_refinement_levels,
            config.initial_radius_factor,
            config.grid_step_factor,
        )
        logger.info(f"使用自动计算参数: 半径={initial_radius}m, 步长={initial_grid_step}m")
    
    # 生成初始网格
    logger.info(f"生成L0初始网格，步长: {initial_grid_step:.2f}m...")
    grid_points = generate_grid_points(bounds, initial_grid_step)
    logger.info(f"生成了 {len(grid_points)} 个L0网格点")
    
    # 创建编排对象
    orchestration_config = {
        "max_refinement_levels": config.max_refinement_levels,
        "initial_radius": initial_radius,
        "min_refinement_radius": config.min_refinement_radius,
        "results_threshold_for_refinement": config.subdivision_threshold,
        "grid_overlap_factor": config.grid_step_factor,
    }
    
    orchestration = create_search_orchestration_with_grid(
        place_type=config.place_type,
        location=config.location,
        grid_points=grid_points,
        search_radius=initial_radius,
        config=orchestration_config,
    )
    
    # 保存初始状态
    state_manager.save_state(orchestration)
    logger.info("初始网格已生成并保存")
    
    return orchestration


def create_grid_engine(orchestration, api_client, state_manager, config):
    """创建网格处理引擎"""
    # 简化的处理配置
    from src.grid_engine import ProcessingConfig
    
    processing_config = ProcessingConfig(
        max_refinement_levels=config.max_refinement_levels,
        initial_radius=orchestration.config.get("initial_radius", config.max_radius),
        min_refinement_radius=config.min_refinement_radius,
        max_api_calls_per_cell=config.max_api_calls_per_cell,
        page_delay_seconds=config.page_delay_seconds,
        results_threshold_for_refinement=config.results_threshold_for_refinement,
        grid_overlap_factor=config.grid_step_factor,
    )
    
    # 简化的可视化回调
    visualization_callback = None
    if config.visualize:
        try:
            from src.visualization_service import VisualizationService
            viz_service = VisualizationService(config.output_dir)
            
            def simple_viz_callback(event_type, data):
                try:
                    if event_type == "layer_complete":
                        viz_service.update_on_layer_complete(data)
                    elif event_type == "refinement_complete":
                        viz_service.update_on_refinement_complete(data)
                except Exception as e:
                    logger.debug(f"可视化更新失败: {e}")
            
            visualization_callback = simple_viz_callback
            logger.info("可视化服务初始化完成")
        except Exception as e:
            logger.warning(f"可视化服务初始化失败: {e}")
    
    return GridEngine(
        orchestration=orchestration,
        api_client=api_client,
        state_manager=state_manager,
        config=processing_config,
        visualization_callback=visualization_callback,
    )


def run_search_loop(grid_engine, api_client, config):
    """执行主搜索循环"""
    start_time = time.time()
    
    logger.info("开始主搜索循环...")
    
    while grid_engine.has_pending_work():
        try:
            has_more, stats = grid_engine.process_next_layer()
            
            # 检查API调用限制
            if api_client.config.api_call_count >= config.max_calls:
                logger.warning(f"达到最大API调用限制 ({config.max_calls})，停止搜索")
                break
            
            # 输出进度信息
            elapsed = time.time() - start_time
            logger.info(f"当前统计: 运行时间={elapsed:.1f}s, API调用={config.api_call_count}, "
                       f"找到地点={len(config.found_places)}, 处理单元={stats.processed_cells}")
            
            if not has_more:
                break
                
        except Exception as e:
            logger.error(f"搜索循环出错: {e}")
            raise SearchError(f"搜索失败: {e}") from e
    
    total_time = time.time() - start_time
    logger.info("主搜索循环完成")
    
    return start_time, total_time


def generate_reports(orchestration, api_client, config, start_time, total_time):
    """生成可视化和报告

    Args:
        orchestration: 搜索编排对象
        api_client: API客户端
        config: 统一配置对象
        start_time: 开始时间（未使用）
        total_time: 总运行时间
    """
    logger.info("生成可视化和报告...")
    
    try:
        # 生成可视化
        if config.visualize:
            logger.info("生成可视化...")
            viz_file = os.path.join(config.output_dir, "visualization_map.html")
            generate_visualization_from_orchestration(
                orchestration, viz_file
            )
            logger.info(f"可视化已保存到 {viz_file}")
            
            # 保存地图数据
            map_data_file = os.path.join(config.output_dir, "map_data.json")
            # 提取坐标数据
            grid_points = [(cell.center_lat, cell.center_lng) for cell in orchestration.cells.values()]
            refinement_points = []  # 简化版本暂不区分细化点
            place_coordinates = []  # 简化版本暂不提取地点坐标

            save_map_data(grid_points, refinement_points, place_coordinates, map_data_file)
            logger.info(f"地图数据已保存到 {map_data_file}")
        
        # 生成最终报告
        logger.info("生成最终报告...")

        # 收集统计信息
        all_place_ids = set()
        for cell in orchestration.cells.values():
            all_place_ids.update(cell.place_ids)

        # 简化的报告输出
        logger.info("\n=== 搜索完成统计 ===")
        logger.info(f"总运行时间: {total_time:.1f} 秒")
        logger.info(f"处理的单元数: {len([c for c in orchestration.cells.values() if c.status != SearchStatus.PENDING])}/{len(orchestration.cells)}")
        logger.info(f"API调用次数: {api_client.config.api_call_count}")
        logger.info(f"找到的唯一地点数: {len(all_place_ids)}")

        # 保存地点ID到文件
        import json
        place_ids_file = os.path.join(config.output_dir, "found_place_ids.json")
        with open(place_ids_file, 'w', encoding='utf-8') as f:
            json.dump(list(all_place_ids), f, indent=2, ensure_ascii=False)
        logger.info(f"结果已保存到: {place_ids_file}")
        logger.info("==================")

        logger.info("最终报告生成完成")
        
    except Exception as e:
        logger.error(f"生成报告失败: {e}")
        raise SearchError(f"报告生成失败: {e}") from e


def main():
    """简化的主程序入口点"""
    try:
        # 1. 解析参数和创建配置
        parser = create_parser()
        args = parser.parse_args()
        
        # 配置日志
        configure_logging(log_level=args.log_level)
        
        logger.info("=== Google Maps Grid Search 启动 ===")
        logger.info(f"工作目录: {args.work_dir}")
        
        # 2. 创建统一配置
        config = create_config(args)
        config.log_summary()
        
        # 3. 创建API客户端
        api_client = create_api_client(config)
        
        # 4. 设置搜索区域
        bounds = setup_search_area(config, api_client)
        
        # 5. 创建状态管理器
        state_manager = StateManager(
            work_dir=config.output_dir,
            state_file="orchestration.json"
        )
        
        # 6. 创建或加载搜索编排对象
        orchestration = create_or_load_orchestration(state_manager, config, bounds)
        
        # 7. 创建网格引擎
        grid_engine = create_grid_engine(orchestration, api_client, state_manager, config)
        
        # 8. 执行搜索
        start_time, total_time = run_search_loop(grid_engine, api_client, config)
        
        # 9. 生成报告
        generate_reports(orchestration, api_client, config, start_time, total_time)
        
        logger.info("=== 搜索完成! ===")
        config.log_summary()
        
    except KeyboardInterrupt:
        logger.info("用户中断操作")
    except (SearchError, ConfigError) as e:
        logger.error(f"搜索失败: {e}")
        raise
    except Exception as e:
        logger.error(f"未知错误: {e}")
        raise SearchError(f"程序执行失败: {e}") from e


if __name__ == "__main__":
    main()
