"""
简化API客户端模块

重构后的API客户端，大幅简化抽象层次和错误处理。
直接集成模拟和真实API，减少不必要的复杂性。
"""

from typing import Dict, Any, Optional

from src.api.mock_client import MockPlacesAPI
from src.api.google_client import GooglePlacesAPI
from src.exceptions import SearchError
from src.logging_config import get_logger

logger = get_logger(__name__)


class APIClient:
    """简化的API客户端
    
    直接管理模拟和真实API，简化错误处理和状态管理。
    """
    
    def __init__(self, config):
        """初始化API客户端
        
        Args:
            config: 统一配置对象
        """
        self.config = config
        
        # 根据配置选择API实现
        if config.dry_run:
            logger.info("使用模拟API客户端")
            self.api = MockPlacesAPI(config.mock_seed)
        else:
            logger.info("使用真实Google Places API客户端")
            if not config.api_key:
                raise SearchError("API_KEY 未在配置中找到")
            self.api = GooglePlacesAPI(config.api_key)
        
        logger.info("API客户端初始化完成")
    
    def perform_nearby_search(
        self,
        lat: float,
        lng: float,
        radius: float,
        place_type: str,
        next_page_token: Optional[str] = None,
        refine_level: int = 0,
    ) -> Dict[str, Any]:
        """执行附近搜索
        
        Args:
            lat: 纬度
            lng: 经度
            radius: 搜索半径（米）
            place_type: 地点类型
            next_page_token: 下一页令牌
            refine_level: 细化层级
            
        Returns:
            Dict[str, Any]: 搜索结果
            
        Raises:
            SearchError: 搜索失败时抛出
        """
        # 检查API调用限制
        if self.config.api_call_count >= self.config.max_calls:
            raise SearchError(f"已达到最大API调用限制 {self.config.max_calls}")
        
        # 增加API调用计数
        self.config.increment_api_calls()
        
        try:
            # 执行API调用
            logger.debug(
                f"执行搜索: lat={lat:.6f}, lng={lng:.6f}, radius={radius}m, "
                f"type={place_type}, token={next_page_token}, level={refine_level}"
            )
            
            result = self.api.perform_nearby_search(
                lat, lng, radius, place_type, next_page_token, refine_level
            )
            
            # 简化的结果处理
            status = result.get("status", "UNKNOWN")
            
            if status == "OK":
                results = result.get("results", [])
                logger.debug(f"搜索成功，找到 {len(results)} 个结果")
                return result
            elif status == "ZERO_RESULTS":
                logger.debug("搜索成功，无结果")
                return result
            else:
                # 所有其他状态都视为错误
                error_msg = f"API调用失败: {status}"
                logger.error(error_msg)
                raise SearchError(error_msg, {"api_status": status, "response": result})
                
        except Exception as e:
            if isinstance(e, SearchError):
                raise
            else:
                error_msg = f"API调用异常: {e}"
                logger.error(error_msg)
                raise SearchError(error_msg) from e
    
    def get_bounding_box(self, location: str) -> Optional[tuple]:
        """获取位置的边界框
        
        Args:
            location: 位置名称
            
        Returns:
            Optional[tuple]: 边界框 (min_lat, min_lng, max_lat, max_lng)
        """
        try:
            if hasattr(self.api, 'get_bounding_box'):
                return self.api.get_bounding_box(location)
            else:
                # 对于没有此方法的API，返回默认边界框
                logger.warning(f"API不支持边界框查询，使用默认边界框")
                return None
        except Exception as e:
            logger.error(f"获取边界框失败: {e}")
            return None


def create_api_client(config) -> APIClient:
    """创建API客户端的工厂函数
    
    Args:
        config: 统一配置对象
        
    Returns:
        APIClient: API客户端实例
    """
    return APIClient(config)
