"""
可视化服务模块

为 Google Maps Grid Search 项目提供安全的、解耦的实时可视化服务。

基于代码审查的架构改进建议，解决解耦、安全性和性能问题。
"""

import os
import time
from pathlib import Path
from typing import Optional, Dict, Any, List, Tuple
from dataclasses import dataclass, field
import logging
import threading

logger = logging.getLogger(__name__)



try:
    import folium

    FOLIUM_AVAILABLE = True
except ImportError:
    FOLIUM_AVAILABLE = False
    logger.warning("Folium 库不可用，将跳过可视化服务")


@dataclass
class VisualizationState:
    """可视化状态内部类"""

    last_update: float = 0.0
    update_count: int = 0
    file_rotation: List[str] = field(default_factory=list)
    enabled: bool = True
    error_count: int = 0


class VisualizationService:
    """解耦的可视化服务

    提供安全的、基于事件的可视化更新服务，不侵入核心处理逻辑。
    """

    def __init__(self, work_dir: str, config: Optional[Dict[str, Any]] = None):
        """初始化可视化服务

        Args:
            work_dir: 工作目录路径
            config: 可视化配置字典
        """
        # 确保config是一个字典，如果不是则使用默认配置
        if not isinstance(config, dict):
            config = {}

        # 使用提供的配置或默认值
        self.config = {
            "enabled": True,
            "update_interval": 5.0,
            "max_file_count": 10,
            "output_filename": "visualization_map",
            "highlight_recent_updates": True,
            "enable_throttling": True,
            **config,
        }
        self.work_dir = self._validate_work_dir(work_dir)
        self.state = VisualizationState()
        self._lock = threading.Lock()

    def _validate_work_dir(self, work_dir: str) -> Path:
        """验证工作目录安全性和合法性"""
        try:
            path = Path(work_dir).resolve()

            # 确保目录存在
            path.mkdir(parents=True, exist_ok=True)

            # 检查是否可写
            if not os.access(path, os.W_OK):
                raise PermissionError(f"工作目录 {path} 不可写")

            return path

        except Exception as e:
            logger.error(f"工作目录验证失败: {e}")
            raise ValueError(f"无效的工作目录: {work_dir}")

    def _should_update(self, force: bool = False) -> bool:
        """检查是否应该更新可视化"""
        if not self.config["enabled"] or not FOLIUM_AVAILABLE:
            return False

        if force:
            return True

        if not self.config["enable_throttling"]:
            return True

        return time.time() - self.state.last_update >= self.config["update_interval"]

    def update_on_cell_processed(
        self, cell, orchestration, force: bool = False
    ) -> bool:
        """网格单元处理完成后更新可视化（节流版）"""
        with self._lock:
            if not self._should_update(force):
                return False

            return self._create_intermediate_visualization(
                orchestration, suffix="realtime", highlight_cell=cell.cell_id
            )

    def update_on_initialization(self, orchestration) -> bool:
        """网格初始化后更新可视化"""
        with self._lock:
            return self._create_intermediate_visualization(
                orchestration, suffix="initial"
            )

    def update_on_refinement(self, orchestration) -> bool:
        """网格细化完成后更新可视化"""
        with self._lock:
            return self._create_intermediate_visualization(
                orchestration, suffix="refinement"
            )

    def _create_intermediate_visualization(
        self, orchestration, suffix: str, highlight_cell: Optional[str] = None
    ) -> bool:
        """创建标准化中间可视化"""
        try:
            if not orchestration or not orchestration.cells:
                logger.debug("没有网格单元数据，跳过可视化")
                return False

            # 清理旧文件
            self._cleanup_old_files()

            # 生成文件名
            filename = f"{self.config['output_filename']}_{suffix}.html"
            output_file = self.work_dir / filename

            # 提取分类数据
            state_data = self._extract_state_data(orchestration)

            # 创建地图边界
            bounds = self._calculate_bounds(orchestration)

            # 创建映射
            if not FOLIUM_AVAILABLE:
                logger.warning("Folium 库不可用，跳过可视化生成")
                return False

            map_obj = folium.Map(
                location=[(bounds[0] + bounds[2]) / 2, (bounds[1] + bounds[3]) / 2],
                zoom_start=13,
                tiles="OpenStreetMap",
            )

            # 添加边界
            self._add_boundary_rectangle(map_obj, bounds)

            # 添加状态标记
            self._add_status_markers(map_obj, state_data)

            # 添加高亮（如果指定）
            if highlight_cell and highlight_cell in orchestration.cells:
                self._add_highlight_marker(map_obj, orchestration.cells[highlight_cell])

            # 添加统计信息
            self._add_statistics_panel(map_obj, orchestration, bounds)

            # 保存文件
            map_obj.save(str(output_file))
            self._record_file_update(str(output_file))

            logger.info(f"可视化已保存: {output_file}")
            return True

        except Exception as e:
            logger.error(f"可视化生成失败: {e}")
            self.state.error_count += 1
            return False

    def _extract_state_data(self, orchestration) -> Dict[str, Any]:
        """从编排对象提取状态数据"""
        from src.data_models import SearchStatus

        states = {
            "pending_nodes": [],
            "processing_nodes": [],
            "completed_nodes": [],
            "failed_nodes": [],
            "refinement_nodes": [],
        }

        for cell in orchestration.cells.values():
            lat, lng = cell.center_lat, cell.center_lng

            if cell.status == SearchStatus.PENDING:
                states["pending_nodes"].append((lat, lng))
            elif cell.status == SearchStatus.PROCESSING:
                states["processing_nodes"].append((lat, lng))
            elif cell.status in [
                SearchStatus.SEARCH_COMPLETE,
                SearchStatus.REFINEMENT_COMPLETE,
            ]:
                states["completed_nodes"].append((lat, lng))
            elif cell.status == SearchStatus.FAILED:
                states["failed_nodes"].append((lat, lng))
            elif cell.status == SearchStatus.REFINEMENT_NEEDED:
                states["refinement_nodes"].append((lat, lng))

        return states

    def _calculate_bounds(self, orchestration) -> Tuple[float, float, float, float]:
        """计算合适的地图边界"""
        if not orchestration.cells:
            return (52.52, 13.405, 52.53, 13.415)  # 默认柏林中心

        cells = list(orchestration.cells.values())
        lats = [c.center_lat for c in cells]
        lngs = [c.center_lng for c in cells]

        padding = 0.01
        return (
            min(lats) - padding,
            min(lngs) - padding,
            max(lats) + padding,
            max(lngs) + padding,
        )

    def _add_boundary_rectangle(self, map_obj, bounds):
        """添加搜索边界矩形"""
        min_lat, min_lng, max_lat, max_lng = bounds
        folium.Rectangle(
            bounds=[[min_lat, min_lng], [max_lat, max_lng]],
            color="black",
            fill=False,
            weight=2,
            popup=f"搜索区域 ({min_lat:.4f}, {min_lng:.4f}) 到 ({max_lat:.4f}, {max_lng:.4f})",
        ).add_to(map_obj)

    def _add_status_markers(self, map_obj, states):
        """添加状态标记"""
        for lat, lng in states["pending_nodes"]:
            folium.CircleMarker(
                location=[lat, lng],
                radius=4,
                color="gray",
                fill=True,
                fillColor="gray",
                fillOpacity=0.6,
                popup=f"待处理网格 ({lat:.4f}, {lng:.4f})",
            ).add_to(map_obj)

        for lat, lng in states["processing_nodes"]:
            folium.CircleMarker(
                location=[lat, lng],
                radius=4,
                color="orange",
                fill=True,
                fillColor="yellow",
                fillOpacity=0.8,
                popup=f"处理中网格 ({lat:.4f}, {lng:.4f})",
            ).add_to(map_obj)

        for lat, lng in states["completed_nodes"]:
            folium.CircleMarker(
                location=[lat, lng],
                radius=4,
                color="green",
                fill=True,
                fillColor="green",
                fillOpacity=0.8,
                popup=f"已完成网格 ({lat:.4f}, {lng:.4f})",
            ).add_to(map_obj)

        for lat, lng in states["failed_nodes"]:
            folium.CircleMarker(
                location=[lat, lng],
                radius=4,
                color="red",
                fill=True,
                fillColor="red",
                fillOpacity=0.8,
                popup=f"失败网格 ({lat:.4f}, {lng:.4f})",
            ).add_to(map_obj)

        for lat, lng in states["refinement_nodes"]:
            folium.CircleMarker(
                location=[lat, lng],
                radius=6,
                color="purple",
                fill=True,
                fillColor="purple",
                fillOpacity=0.8,
                popup=f"需细化网格 ({lat:.4f}, {lng:.4f})",
            ).add_to(map_obj)

    def _add_highlight_marker(self, map_obj, cell):
        """添加高亮标记"""
        lat, lng = cell.center_lat, cell.center_lng
        folium.CircleMarker(
            location=[lat, lng],
            radius=8,
            color="gold",
            fill=True,
            fillColor="gold",
            fillOpacity=0.9,
            weight=3,
            popup=f"最近更新 ({lat:.4f}, {lng:.4f}) - 状态: {cell.status.value}",
        ).add_to(map_obj)

    def _add_statistics_panel(self, map_obj, orchestration, bounds):
        """添加统计信息面板"""
        center_lat = (bounds[0] + bounds[2]) / 2
        center_lng = (bounds[1] + bounds[3]) / 2

        stats = orchestration.get_summary()
        processing_progress = f"""
        实时搜索进度:
        - 总网格单元: {len(orchestration.cells)}
        - 待处理: {len(orchestration.get_pending_cells())}
        - 当前层级: {orchestration.current_layer}
        - 完成层级: {len(orchestration.completed_layers)}
        - 发现地点: {len(orchestration.get_all_place_ids())}
        - API调用: {orchestration.metrics.get('total_api_calls', 0)}
        - 总体状态: {orchestration.get_overall_status()}
        """

        folium.Marker(
            location=[center_lat, center_lng],
            popup=processing_progress,
            icon=folium.Icon(color="blue", icon="info-sign"),
        ).add_to(map_obj)

        # 添加图例
        legend_html = """
        <div style="position: fixed; 
                    top: 10px; right: 10px; width: 180px; min-height: 150px; 
                    background-color:white; border:2px solid grey; z-index:9999; 
                    font-size:12px; padding: 10px
                    ">
        <b>实时图例</b><br>
        <i style="color:gray">●</i> 待处理<br>
        <i style="color:orange">●</i> 处理中<br>
        <i style="color:green">●</i> 已完成<br>
        <i style="color:red">●</i> 失败<br>
        <i style="color:purple">●</i> 需细化<br>
        <i style="color:gold">●</i> 最近更新
        </div>
        """
        map_obj.get_root().html.add_child(folium.Element(legend_html))

    def _cleanup_old_files(self):
        """清理旧的可视化文件"""
        try:
            # 保留最近的X个文件
            pattern = f"{self.config['output_filename']}_*.html"
            files = list(self.work_dir.glob(pattern))

            # 按修改时间排序
            files.sort(key=lambda x: x.stat().st_mtime, reverse=True)

            # 删除多余文件
            for file_path in files[self.config["max_file_count"] :]:
                try:
                    file_path.unlink()
                    logger.debug(f"清理旧文件: {file_path}")
                except OSError:
                    pass

        except Exception as e:
            logger.debug(f"文件清理失败: {e}")

    def _record_file_update(self, file_path: str):
        """记录文件更新"""
        self.state.file_rotation.insert(0, file_path)
        self.state.file_rotation = self.state.file_rotation[
            : self.config["max_file_count"]
        ]
        self.state.last_update = time.time()
        self.state.update_count += 1

    def get_stats(self) -> Dict[str, Any]:
        """获取可视化服务统计信息"""
        return {
            "enabled": self.config["enabled"],
            "update_count": self.state.update_count,
            "error_count": self.state.error_count,
            "last_update": self.state.last_update,
            "work_dir": str(self.work_dir),
            "config": {
                "update_interval": self.config["update_interval"],
                "max_file_count": self.config["max_file_count"],
            },
        }

    def reset(self):
        """重置服务状态"""
        with self._lock:
            self.state = VisualizationState()
